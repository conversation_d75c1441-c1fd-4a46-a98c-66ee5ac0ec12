// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/asr/ui/asr_screen.dart';
import '../../features/info/ui/info_screen.dart';

/// 主屏幕，包含底部导航
class MainScreen extends StatefulWidget {
  const MainScreen({super.key, required this.child});

  final Widget child;

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VocalMind AI'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (int index) {
          setState(() {
            _currentIndex = index;
          });
          switch (index) {
            case 0:
              context.go(AppRoutes.home);
              break;
            case 1:
              context.go(AppRoutes.meetings);
              break;
            case 2:
              context.go(AppRoutes.todo);
              break;
            case 3:
              context.go(AppRoutes.settings);
              break;
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.mic),
            label: '语音识别',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: '会议记录',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.task_alt),
            label: '待办事项',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: '设置',
          ),
        ],
      ),
    );
  }
}

/// 应用路由配置
///
/// 使用go_router实现中心化的路由管理
final GoRouter appRouter = GoRouter(
  initialLocation: '/',
  routes: [
    // 认证相关路由
    GoRoute(
      path: '/welcome',
      name: 'welcome',
      builder: (context, state) => const Placeholder(), // TODO: WelcomeScreen
    ),
    GoRoute(
      path: '/login',
      name: 'login',
      builder: (context, state) => const Placeholder(), // TODO: LoginScreen
    ),
    GoRoute(
      path: '/register',
      name: 'register',
      builder: (context, state) => const Placeholder(), // TODO: RegisterScreen
    ),

    // 主应用路由
    ShellRoute(
      builder: (context, state, child) => MainScreen(child: child),
      routes: [
        GoRoute(
          path: '/',
          name: 'home',
          builder: (context, state) => const AsrScreen(),
        ),
        GoRoute(
          path: '/meetings',
          name: 'meetings',
          builder: (context, state) => const Placeholder(), // TODO: MeetingListScreen
        ),
        GoRoute(
          path: '/meeting/:id',
          name: 'meeting_detail',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            // TODO: Replace with actual MeetingDetailScreen(id: id)
            return Placeholder(
              child: Center(
                child: Text('Meeting Detail: $id'),
              ),
            );
          },
        ),
        GoRoute(
          path: '/ai_chat',
          name: 'ai_chat',
          builder: (context, state) => const Placeholder(), // TODO: AiChatScreen
        ),
        GoRoute(
          path: '/todo',
          name: 'todo',
          builder: (context, state) => const Placeholder(), // TODO: TodoListScreen
        ),
        GoRoute(
          path: '/todo/edit',
          name: 'todo_edit',
          builder: (context, state) => const Placeholder(), // TODO: TodoEditScreen
        ),
        GoRoute(
          path: '/settings',
          name: 'settings',
          builder: (context, state) => const Placeholder(), // TODO: SettingsScreen
        ),
        GoRoute(
          path: '/info',
          name: 'info',
          builder: (context, state) => const InfoScreen(),
        ),
      ],
    ),
  ],
);

/// 路由路径常量
class AppRoutes {
  // 认证路由
  static const String welcome = '/welcome';
  static const String login = '/login';
  static const String register = '/register';

  // 主应用路由
  static const String home = '/';
  static const String meetings = '/meetings';
  static const String meetingDetail = '/meeting';
  static const String aiChat = '/ai_chat';
  static const String todo = '/todo';
  static const String todoEdit = '/todo/edit';
  static const String settings = '/settings';
  static const String info = '/info';

  /// 构建会议详情路由
  static String buildMeetingDetailRoute(String id) => '/meeting/$id';
}
