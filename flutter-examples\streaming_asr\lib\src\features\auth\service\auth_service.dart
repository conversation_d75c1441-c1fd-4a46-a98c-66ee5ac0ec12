// Copyright (c) 2024 VocalMind AI
import '../../../core/api/api_client.dart';
import '../../../core/api/api_response.dart';
import '../../../core/log/app_logger.dart';
import '../../../shared/model/app_error.dart';
import '../../../shared/service/storage_service.dart';
import '../model/auth_models.dart';

/// 认证服务
/// 
/// 处理用户认证相关的API调用和本地存储
class AuthService {
  static const String _tag = 'AuthService';
  
  final ApiClient _apiClient;
  final StorageService _storageService;
  
  AuthService({
    ApiClient? apiClient,
    StorageService? storageService,
  }) : _apiClient = apiClient ?? ApiClient.instance,
       _storageService = storageService ?? StorageService.instance;

  /// 用户登录
  Future<ErrorResult<AuthResponse>> login(LoginRequest request) async {
    try {
      AppLogger.info(_tag, 'Attempting login for: ${request.email}');
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/auth/login',
        data: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      
      if (response.isSuccess && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);
        
        // 保存认证信息到本地存储
        await _saveAuthInfo(authResponse);
        
        AppLogger.info(_tag, 'Login successful for: ${request.email}');
        return ErrorResult.success(authResponse);
      } else {
        final error = AppError.authentication(
          response.errorMessage,
          code: 'LOGIN_FAILED',
        );
        AppLogger.warning(_tag, 'Login failed: ${response.errorMessage}');
        return ErrorResult.failure(error);
      }
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Login error', e, stackTrace);
      final error = AppError.network(
        'Login request failed',
        originalError: e,
      );
      return ErrorResult.failure(error);
    }
  }

  /// 用户注册
  Future<ErrorResult<AuthResponse>> register(RegisterRequest request) async {
    try {
      AppLogger.info(_tag, 'Attempting registration for: ${request.email}');
      
      // 验证请求
      if (!request.isValid) {
        final error = AppError.validation(
          'Registration data is invalid',
          code: 'INVALID_DATA',
        );
        return ErrorResult.failure(error);
      }
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/auth/register',
        data: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      
      if (response.isSuccess && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);
        
        // 保存认证信息到本地存储
        await _saveAuthInfo(authResponse);
        
        AppLogger.info(_tag, 'Registration successful for: ${request.email}');
        return ErrorResult.success(authResponse);
      } else {
        final error = AppError.authentication(
          response.errorMessage,
          code: 'REGISTRATION_FAILED',
        );
        AppLogger.warning(_tag, 'Registration failed: ${response.errorMessage}');
        return ErrorResult.failure(error);
      }
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Registration error', e, stackTrace);
      final error = AppError.network(
        'Registration request failed',
        originalError: e,
      );
      return ErrorResult.failure(error);
    }
  }

  /// 忘记密码
  Future<ErrorResult<bool>> forgotPassword(ForgotPasswordRequest request) async {
    try {
      AppLogger.info(_tag, 'Sending password reset email to: ${request.email}');
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/auth/forgot-password',
        data: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      
      if (response.isSuccess) {
        AppLogger.info(_tag, 'Password reset email sent to: ${request.email}');
        return ErrorResult.success(true);
      } else {
        final error = AppError.server(
          response.errorMessage,
          code: 'FORGOT_PASSWORD_FAILED',
        );
        AppLogger.warning(_tag, 'Forgot password failed: ${response.errorMessage}');
        return ErrorResult.failure(error);
      }
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Forgot password error', e, stackTrace);
      final error = AppError.network(
        'Forgot password request failed',
        originalError: e,
      );
      return ErrorResult.failure(error);
    }
  }

  /// 重置密码
  Future<ErrorResult<bool>> resetPassword(ResetPasswordRequest request) async {
    try {
      AppLogger.info(_tag, 'Attempting password reset');
      
      // 验证密码匹配
      if (!request.passwordsMatch) {
        final error = AppError.validation(
          'Passwords do not match',
          code: 'PASSWORD_MISMATCH',
        );
        return ErrorResult.failure(error);
      }
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/auth/reset-password',
        data: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      
      if (response.isSuccess) {
        AppLogger.info(_tag, 'Password reset successful');
        return ErrorResult.success(true);
      } else {
        final error = AppError.server(
          response.errorMessage,
          code: 'RESET_PASSWORD_FAILED',
        );
        AppLogger.warning(_tag, 'Password reset failed: ${response.errorMessage}');
        return ErrorResult.failure(error);
      }
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Password reset error', e, stackTrace);
      final error = AppError.network(
        'Password reset request failed',
        originalError: e,
      );
      return ErrorResult.failure(error);
    }
  }

  /// 刷新Token
  Future<ErrorResult<AuthResponse>> refreshToken(String refreshToken) async {
    try {
      AppLogger.info(_tag, 'Refreshing access token');
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
        fromJson: (data) => data as Map<String, dynamic>,
      );
      
      if (response.isSuccess && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);
        
        // 更新本地存储的认证信息
        await _saveAuthInfo(authResponse);
        
        AppLogger.info(_tag, 'Token refresh successful');
        return ErrorResult.success(authResponse);
      } else {
        final error = AppError.authentication(
          response.errorMessage,
          code: 'TOKEN_REFRESH_FAILED',
        );
        AppLogger.warning(_tag, 'Token refresh failed: ${response.errorMessage}');
        return ErrorResult.failure(error);
      }
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Token refresh error', e, stackTrace);
      final error = AppError.network(
        'Token refresh request failed',
        originalError: e,
      );
      return ErrorResult.failure(error);
    }
  }

  /// 登出
  Future<ErrorResult<bool>> logout() async {
    try {
      AppLogger.info(_tag, 'Logging out user');
      
      // 尝试调用服务器登出接口
      try {
        await _apiClient.post('/auth/logout');
      } catch (e) {
        // 即使服务器登出失败，也要清除本地数据
        AppLogger.warning(_tag, 'Server logout failed, but continuing with local cleanup');
      }
      
      // 清除本地认证信息
      await _clearAuthInfo();
      
      AppLogger.info(_tag, 'Logout successful');
      return ErrorResult.success(true);
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Logout error', e, stackTrace);
      final error = AppError.unknown(
        'Logout failed',
        originalError: e,
        stackTrace: stackTrace,
      );
      return ErrorResult.failure(error);
    }
  }

  /// 获取本地存储的认证信息
  Future<AuthState?> getStoredAuthInfo() async {
    try {
      final authData = _storageService.getJson(StorageKeys.authToken);
      if (authData == null) return null;
      
      final accessToken = authData['access_token'] as String?;
      final refreshToken = authData['refresh_token'] as String?;
      final expiresAtMs = authData['expires_at'] as int?;
      final userData = authData['user'] as Map<String, dynamic>?;
      
      if (accessToken == null || refreshToken == null || expiresAtMs == null) {
        return null;
      }
      
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(expiresAtMs);
      
      return AuthState.authenticated(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresAt: expiresAt,
        user: userData ?? {},
      );
    } catch (e) {
      AppLogger.error(_tag, 'Failed to get stored auth info', e);
      return null;
    }
  }

  /// 保存认证信息到本地存储
  Future<void> _saveAuthInfo(AuthResponse authResponse) async {
    try {
      final authData = {
        'access_token': authResponse.accessToken,
        'refresh_token': authResponse.refreshToken,
        'expires_at': authResponse.expiresAt.millisecondsSinceEpoch,
        'token_type': authResponse.tokenType,
        'user': authResponse.user,
      };
      
      await _storageService.setJson(StorageKeys.authToken, authData);
      
      // 设置API客户端的认证Token
      _apiClient.setAuthToken(authResponse.accessToken);
      
      AppLogger.debug(_tag, 'Auth info saved to local storage');
    } catch (e) {
      AppLogger.error(_tag, 'Failed to save auth info', e);
      throw AppError.storage(
        'Failed to save authentication information',
        originalError: e,
      );
    }
  }

  /// 清除本地认证信息
  Future<void> _clearAuthInfo() async {
    try {
      await _storageService.remove(StorageKeys.authToken);
      await _storageService.remove(StorageKeys.userInfo);
      
      // 清除API客户端的认证Token
      _apiClient.clearAuthToken();
      
      AppLogger.debug(_tag, 'Auth info cleared from local storage');
    } catch (e) {
      AppLogger.error(_tag, 'Failed to clear auth info', e);
      throw AppError.storage(
        'Failed to clear authentication information',
        originalError: e,
      );
    }
  }
}
