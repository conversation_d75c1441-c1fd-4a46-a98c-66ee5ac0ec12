// Copyright (c) 2024 VocalMind AI
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/base/base_controller.dart';
import '../../../core/log/app_logger.dart';
import '../../../shared/model/app_error.dart';
import '../service/auth_service.dart';
import '../model/auth_models.dart';

/// 认证控制器
/// 
/// 管理用户认证状态和相关操作
class AuthController extends BaseController<AuthState> {
  final AuthService _authService;
  
  AuthController({AuthService? authService}) 
      : _authService = authService ?? AuthService(),
        super();

  @override
  String get tag => 'AuthController';

  @override
  void onInit() {
    super.onInit();
    // 初始化时检查本地存储的认证信息
    _checkStoredAuth();
  }

  /// 检查本地存储的认证信息
  Future<void> _checkStoredAuth() async {
    try {
      AppLogger.info(tag, 'Checking stored authentication info');
      
      final storedAuth = await _authService.getStoredAuthInfo();
      if (storedAuth != null) {
        if (storedAuth.isTokenExpired) {
          // Token已过期，尝试刷新
          AppLogger.info(tag, 'Token expired, attempting refresh');
          await _refreshToken(storedAuth.refreshToken!);
        } else {
          // Token有效，设置为已认证状态
          setSuccess(storedAuth);
          AppLogger.info(tag, 'Valid token found, user authenticated');
        }
      } else {
        // 没有存储的认证信息
        setSuccess(AuthState.unauthenticated());
        AppLogger.info(tag, 'No stored authentication info found');
      }
    } catch (e) {
      AppLogger.error(tag, 'Error checking stored auth', e);
      setSuccess(AuthState.unauthenticated());
    }
  }

  /// 用户登录
  Future<void> login(String email, String password, {bool rememberMe = false}) async {
    await safeExecute(
      () async {
        final request = LoginRequest(
          email: email,
          password: password,
          rememberMe: rememberMe,
        );
        
        final result = await _authService.login(request);
        
        if (result.isSuccess) {
          final authResponse = result.result;
          return AuthState.authenticated(
            accessToken: authResponse.accessToken,
            refreshToken: authResponse.refreshToken,
            expiresAt: authResponse.expiresAt,
            user: authResponse.user,
          );
        } else {
          throw result.errorValue;
        }
      },
      errorMessage: 'Login failed',
      onSuccess: (authState) {
        AppLogger.info(tag, 'Login successful');
      },
      onError: (error) {
        AppLogger.error(tag, 'Login failed: $error');
      },
    );
  }

  /// 用户注册
  Future<void> register({
    required String email,
    required String password,
    required String confirmPassword,
    String? name,
    bool agreeToTerms = false,
  }) async {
    await safeExecute(
      () async {
        final request = RegisterRequest(
          email: email,
          password: password,
          confirmPassword: confirmPassword,
          name: name,
          agreeToTerms: agreeToTerms,
        );
        
        final result = await _authService.register(request);
        
        if (result.isSuccess) {
          final authResponse = result.result;
          return AuthState.authenticated(
            accessToken: authResponse.accessToken,
            refreshToken: authResponse.refreshToken,
            expiresAt: authResponse.expiresAt,
            user: authResponse.user,
          );
        } else {
          throw result.errorValue;
        }
      },
      errorMessage: 'Registration failed',
      onSuccess: (authState) {
        AppLogger.info(tag, 'Registration successful');
      },
      onError: (error) {
        AppLogger.error(tag, 'Registration failed: $error');
      },
    );
  }

  /// 忘记密码
  Future<bool> forgotPassword(String email) async {
    bool success = false;
    
    await safeExecute(
      () async {
        final request = ForgotPasswordRequest(email: email);
        final result = await _authService.forgotPassword(request);
        
        if (result.isSuccess) {
          success = true;
          return state.data ?? AuthState.unauthenticated();
        } else {
          throw result.errorValue;
        }
      },
      showLoading: false, // 不改变主要状态
      errorMessage: 'Failed to send password reset email',
      onSuccess: (_) {
        AppLogger.info(tag, 'Password reset email sent');
      },
      onError: (error) {
        AppLogger.error(tag, 'Forgot password failed: $error');
      },
    );
    
    return success;
  }

  /// 重置密码
  Future<bool> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    bool success = false;
    
    await safeExecute(
      () async {
        final request = ResetPasswordRequest(
          token: token,
          newPassword: newPassword,
          confirmPassword: confirmPassword,
        );
        
        final result = await _authService.resetPassword(request);
        
        if (result.isSuccess) {
          success = true;
          return state.data ?? AuthState.unauthenticated();
        } else {
          throw result.errorValue;
        }
      },
      showLoading: false, // 不改变主要状态
      errorMessage: 'Failed to reset password',
      onSuccess: (_) {
        AppLogger.info(tag, 'Password reset successful');
      },
      onError: (error) {
        AppLogger.error(tag, 'Password reset failed: $error');
      },
    );
    
    return success;
  }

  /// 刷新Token
  Future<void> _refreshToken(String refreshToken) async {
    try {
      final result = await _authService.refreshToken(refreshToken);
      
      if (result.isSuccess) {
        final authResponse = result.result;
        final newAuthState = AuthState.authenticated(
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
          expiresAt: authResponse.expiresAt,
          user: authResponse.user,
        );
        setSuccess(newAuthState);
        AppLogger.info(tag, 'Token refresh successful');
      } else {
        // 刷新失败，设置为未认证状态
        setSuccess(AuthState.unauthenticated());
        AppLogger.warning(tag, 'Token refresh failed: ${result.errorValue.message}');
      }
    } catch (e) {
      AppLogger.error(tag, 'Token refresh error', e);
      setSuccess(AuthState.unauthenticated());
    }
  }

  /// 登出
  Future<void> logout() async {
    await safeExecute(
      () async {
        final result = await _authService.logout();
        
        if (result.isSuccess) {
          return AuthState.unauthenticated();
        } else {
          // 即使服务器登出失败，也要清除本地状态
          AppLogger.warning(tag, 'Server logout failed, but clearing local state');
          return AuthState.unauthenticated();
        }
      },
      errorMessage: 'Logout failed',
      onSuccess: (_) {
        AppLogger.info(tag, 'Logout successful');
      },
      onError: (error) {
        AppLogger.error(tag, 'Logout error: $error');
        // 强制设置为未认证状态
        setSuccess(AuthState.unauthenticated());
      },
    );
  }

  /// 检查并刷新Token（如果需要）
  Future<void> checkAndRefreshToken() async {
    final currentState = state.data;
    if (currentState != null && 
        currentState.isAuthenticated && 
        currentState.isTokenExpiringSoon) {
      AppLogger.info(tag, 'Token expiring soon, refreshing...');
      await _refreshToken(currentState.refreshToken!);
    }
  }

  /// 获取当前认证状态
  AuthState? get currentAuthState => state.data;

  /// 检查是否已认证
  bool get isAuthenticated => currentAuthState?.isAuthenticated ?? false;

  /// 获取当前用户信息
  Map<String, dynamic>? get currentUser => currentAuthState?.user;

  /// 获取访问Token
  String? get accessToken => currentAuthState?.accessToken;

  /// 获取Authorization头部
  String? get authorizationHeader => currentAuthState?.authorizationHeader;
}

/// 认证控制器Provider
final authControllerProvider = StateNotifierProvider<AuthController, BaseStateData<AuthState>>((ref) {
  return AuthController();
});

/// 认证状态Provider（便捷访问）
final authStateProvider = Provider<AuthState?>((ref) {
  return ref.watch(authControllerProvider).data;
});

/// 是否已认证Provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState?.isAuthenticated ?? false;
});

/// 当前用户Provider
final currentUserProvider = Provider<Map<String, dynamic>?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState?.user;
});
