# VocalMind AI Flutter Migration TODO List

## 阶段1: 基础架构搭建 (1-2周)

### 1.1 Core层基础设施完善
- [ ] **API客户端封装** (`core/api/`)
  - [ ] 创建基础HTTP客户端 (使用dio)
  - [ ] 实现请求/响应拦截器
  - [ ] 错误处理机制
  - [ ] API响应模型基类
  - [ ] 网络状态监控

- [ ] **基础控制器类** (`core/base/`)
  - [ ] BaseController抽象类
  - [ ] BaseNotifier (Riverpod)
  - [ ] 生命周期管理
  - [ ] 错误状态处理
  - [ ] 加载状态管理

- [ ] **日志服务** (`core/log/`)
  - [ ] 日志级别配置
  - [ ] 文件日志输出
  - [ ] 网络日志上传
  - [ ] 调试模式开关

- [ ] **路由系统** (`core/router/`)
  - [ ] GoRouter配置
  - [ ] 路由守卫 (认证检查)
  - [ ] 深链接支持
  - [ ] 路由动画配置

- [ ] **工具函数** (`core/utils/`)
  - [ ] 时间格式化工具
  - [ ] 文件操作工具
  - [ ] 加密解密工具
  - [ ] 设备信息获取

### 1.2 Shared层共享组件
- [ ] **共享UI组件** (`shared/ui/`)
  - [ ] 自定义按钮组件
  - [ ] 加载指示器
  - [ ] 错误提示组件
  - [ ] 对话框组件
  - [ ] 底部导航栏

- [ ] **共享模型** (`shared/model/`)
  - [ ] 用户信息模型
  - [ ] API响应基础模型
  - [ ] 错误信息模型
  - [ ] 分页数据模型

- [ ] **共享服务** (`shared/service/`)
  - [ ] 本地存储服务
  - [ ] 权限管理服务
  - [ ] 设备信息服务
  - [ ] 文件管理服务

### 1.3 依赖包集成
- [ ] **添加必要依赖**
  - [ ] dio (网络请求)
  - [ ] sqflite (本地数据库)
  - [ ] shared_preferences (简单存储)
  - [ ] flutter_local_notifications (通知)
  - [ ] file_picker (文件选择)
  - [ ] permission_handler (权限管理)
  - [ ] flutter_background_service (后台服务)

## 阶段2: 核心功能迁移 (3-4周)

### 2.1 用户认证模块 (`features/auth/`)
- [ ] **认证UI页面**
  - [ ] WelcomeScreen (欢迎页面)
  - [ ] LoginScreen (登录页面)
  - [ ] RegisterScreen (注册页面)
  - [ ] ForgotPasswordScreen (忘记密码)

- [ ] **认证控制器**
  - [ ] AuthController (认证状态管理)
  - [ ] 登录/注册逻辑
  - [ ] 认证状态持久化
  - [ ] 自动登录功能

- [ ] **认证服务**
  - [ ] AuthService (API调用)
  - [ ] Token管理
  - [ ] 用户信息缓存
  - [ ] 登出清理

- [ ] **认证模型**
  - [ ] User模型
  - [ ] AuthState模型
  - [ ] LoginRequest/Response模型

### 2.2 语音识别模块 (`features/asr/`)
- [ ] **ASR UI界面**
  - [ ] AsrScreen (主录音界面)
  - [ ] RecordingWidget (录音控制组件)
  - [ ] TranscriptionWidget (转录结果显示)
  - [ ] AudioVisualizerWidget (音频可视化)

- [ ] **ASR控制器**
  - [ ] AsrController (录音状态管理)
  - [ ] 录音开始/停止逻辑
  - [ ] 实时转录结果处理
  - [ ] 音频文件导入处理

- [ ] **ASR服务**
  - [ ] SherpaOnnxService (语音识别引擎)
  - [ ] AudioRecordingService (录音服务)
  - [ ] AudioFileService (音频文件处理)
  - [ ] 后台录音服务集成

- [ ] **ASR模型**
  - [ ] TranscriptionResult模型
  - [ ] RecordingState模型
  - [ ] AudioConfig模型

### 2.3 会议记录管理模块 (`features/meeting/`)
- [ ] **会议记录UI**
  - [ ] MeetingListScreen (记录列表)
  - [ ] MeetingDetailScreen (记录详情)
  - [ ] MeetingSearchWidget (搜索组件)

- [ ] **会议记录控制器**
  - [ ] MeetingController (记录管理)
  - [ ] 记录保存逻辑
  - [ ] 搜索和筛选
  - [ ] 导出功能

- [ ] **会议记录服务**
  - [ ] MeetingStorageService (本地存储)
  - [ ] 数据库操作
  - [ ] 文件导出服务

- [ ] **会议记录模型**
  - [ ] MeetingRecord模型
  - [ ] MeetingTag模型
  - [ ] SearchFilter模型

## 阶段3: 高级功能实现 (2-3周)

### 3.1 AI交互模块 (`features/ai_chat/`)
- [ ] **AI聊天UI**
  - [ ] AiChatScreen (聊天界面)
  - [ ] ChatMessageWidget (消息组件)
  - [ ] ChatInputWidget (输入组件)

- [ ] **AI聊天控制器**
  - [ ] AiChatController (聊天管理)
  - [ ] 消息发送/接收
  - [ ] 聊天历史管理

- [ ] **AI聊天服务**
  - [ ] LLMService (大语言模型API)
  - [ ] 多模型支持
  - [ ] 聊天历史存储

- [ ] **AI聊天模型**
  - [ ] ChatMessage模型
  - [ ] ChatSession模型
  - [ ] LLMConfig模型

### 3.2 待办事项模块 (`features/todo/`)
- [ ] **待办事项UI**
  - [ ] TodoListScreen (待办列表)
  - [ ] TodoEditScreen (编辑页面)
  - [ ] TodoWidget (待办项组件)

- [ ] **待办事项控制器**
  - [ ] TodoController (待办管理)
  - [ ] 自动生成待办
  - [ ] 提醒功能

- [ ] **待办事项服务**
  - [ ] TodoStorageService (本地存储)
  - [ ] NotificationService (通知服务)
  - [ ] ReminderService (提醒服务)

- [ ] **待办事项模型**
  - [ ] TodoItem模型
  - [ ] TodoCategory模型
  - [ ] ReminderConfig模型

### 3.3 设置模块 (`features/settings/`)
- [ ] **设置UI**
  - [ ] SettingsScreen (主设置页面)
  - [ ] ServerSettingsScreen (服务器设置)
  - [ ] LLMSettingsScreen (AI模型设置)

- [ ] **设置控制器**
  - [ ] SettingsController (设置管理)
  - [ ] 配置保存/加载
  - [ ] 设置验证

- [ ] **设置服务**
  - [ ] ConfigService (配置管理)
  - [ ] 设置同步服务

- [ ] **设置模型**
  - [ ] AppSettings模型
  - [ ] ServerConfig模型
  - [ ] LLMConfig模型

## 阶段4: 优化和完善 (1-2周)

### 4.1 性能优化
- [ ] **内存优化**
  - [ ] 音频数据流优化
  - [ ] 大文件处理优化
  - [ ] Widget重建优化

- [ ] **网络优化**
  - [ ] 请求缓存机制
  - [ ] 离线模式支持
  - [ ] 网络重试机制

- [ ] **存储优化**
  - [ ] 数据库索引优化
  - [ ] 文件压缩存储
  - [ ] 缓存清理机制

### 4.2 UI/UX改进
- [ ] **界面优化**
  - [ ] 响应式布局适配
  - [ ] 暗色主题支持
  - [ ] 动画效果优化

- [ ] **交互优化**
  - [ ] 手势操作支持
  - [ ] 快捷键支持
  - [ ] 无障碍功能

### 4.3 测试和调试
- [ ] **单元测试**
  - [ ] Controller测试
  - [ ] Service测试
  - [ ] Model测试

- [ ] **Widget测试**
  - [ ] UI组件测试
  - [ ] 交互测试
  - [ ] 状态测试

- [ ] **集成测试**
  - [ ] 端到端流程测试
  - [ ] 性能测试
  - [ ] 兼容性测试

### 4.4 文档完善
- [ ] **技术文档**
  - [ ] API文档
  - [ ] 架构文档
  - [ ] 部署文档

- [ ] **用户文档**
  - [ ] 使用说明
  - [ ] 常见问题
  - [ ] 更新日志

## 关键里程碑

### 里程碑1: 基础架构完成 (第2周末)
- [ ] Core层基础设施完善
- [ ] Shared层共享组件就绪
- [ ] 路由和状态管理配置完成

### 里程碑2: 核心功能可用 (第6周末)
- [ ] 用户认证流程完整
- [ ] 语音识别基本功能可用
- [ ] 会议记录管理基础功能

### 里程碑3: 功能完整性 (第9周末)
- [ ] 所有主要功能模块完成
- [ ] AI交互和待办事项功能可用
- [ ] 设置功能完整

### 里程碑4: 产品就绪 (第11周末)
- [ ] 性能优化完成
- [ ] 测试覆盖率达标
- [ ] 文档完善
- [ ] 可发布版本就绪

## 风险缓解措施

### 技术风险
- [ ] sherpa-onnx集成问题 → 提前验证和测试
- [ ] 后台录音权限问题 → 研究最佳实践
- [ ] 内存管理问题 → 性能监控和优化

### 进度风险
- [ ] 功能复杂度超预期 → 分阶段交付
- [ ] 技术难点阻塞 → 并行开发和备选方案
- [ ] 测试时间不足 → 开发过程中持续测试

### 质量风险
- [ ] 用户体验下降 → 原型验证和用户反馈
- [ ] 性能不达标 → 性能基准测试
- [ ] 稳定性问题 → 充分的集成测试

## 当前优先级 (本次对话)

### 立即开始 (高优先级)
1. [ ] **完善Core层API客户端** - 为后续模块提供基础
2. [ ] **建立用户认证模块** - 应用入口功能
3. [ ] **优化ASR模块** - 核心功能改进

### 下一步计划 (中优先级)
1. [ ] **会议记录管理** - 数据持久化
2. [ ] **共享UI组件库** - 提高开发效率

### 后续规划 (低优先级)
1. [ ] **AI交互功能** - 高级功能
2. [ ] **待办事项管理** - 扩展功能
